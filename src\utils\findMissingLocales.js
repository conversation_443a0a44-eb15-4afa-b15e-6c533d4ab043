// 检查缺失的locale配置
const fs = require('fs');
const path = require('path');

// 从REGION_GROUPS中定义的所有locale
const allLocalesInGroups = [
  // 亚洲
  'zh_CN', 'zh_TW', 'ja_JP', 'ko_KR', 'en_HK', 'en_SG', 'ms_MY', 'th_TH', 
  'vi_VN', 'en_PH', 'id_ID', 'en_IN', 'bn_BD', 'ne_NP', 'fa_IR', 'he_IL', 
  'tr_TR', 'kk_KZ', 'mn_MN', 'hy_AM', 'ka_GE',
  // 欧洲
  'en_GB', 'de_DE', 'fr_FR', 'it_IT', 'es_ES', 'ru_RU', 'pl_PL', 'nl_NL', 
  'sv_SE', 'nb_NO', 'da_DK', 'fi_FI', 'is_IS', 'at_AT', 'de_AT', 'de_CH', 
  'fr_CH', 'it_CH', 'fr_BE', 'nl_BE', 'pt_PT', 'el_GR', 'el_CY', 'cs_CZ', 
  'sk_SK', 'hu_HU', 'ro_RO', 'ro_MD', 'bg_BG', 'hr_HR', 'sl_SI', 'sr_RS', 
  'sr_Latn_RS', 'sr_Cyrl_RS', 'me_ME', 'lt_LT', 'lv_LV', 'et_EE', 'uk_UA',
  // 北美洲
  'en_US', 'en_CA', 'fr_CA',
  // 南美洲
  'pt_BR', 'es_AR', 'es_PE', 'es_VE',
  // 非洲
  'ar_EG', 'en_ZA', 'en_NG', 'en_UG',
  // 大洋洲
  'en_AU', 'en_NZ',
  // 中东
  'ar_JO', 'ar_SA'
];

// 从LOCALE_CONFIG中实际存在的locale
const existingLocales = [
  'zh_CN', 'zh_TW', 'ja_JP', 'ko_KR', 'en_HK', 'en_SG', 'ms_MY', 'th_TH',
  'vi_VN', 'en_PH', 'id_ID', 'en_IN', 'bn_BD', 'ne_NP', 'fa_IR', 'he_IL',
  'tr_TR', 'kk_KZ', 'mn_MN', 'hy_AM', 'ka_GE', 'en_GB', 'de_DE', 'fr_FR',
  'it_IT', 'es_ES', 'ru_RU', 'pl_PL', 'nl_NL', 'sv_SE', 'nb_NO', 'da_DK',
  'fi_FI', 'is_IS', 'at_AT', 'de_AT', 'en_US', 'en_CA', 'fr_CA', 'pt_BR',
  'es_AR', 'ar_EG', 'en_ZA', 'en_AU', 'en_NZ', 'ar_JO', 'ar_SA'
];

// 找出缺失的locale
const missingLocales = allLocalesInGroups.filter(locale => !existingLocales.includes(locale));

console.log('缺失的locale配置：');
console.log(missingLocales);
console.log(`\n总共缺失 ${missingLocales.length} 个locale配置`);

// 按地区分组显示缺失的locale
const missingByRegion = {
  '欧洲': ['de_CH', 'fr_CH', 'it_CH', 'fr_BE', 'nl_BE', 'pt_PT', 'el_GR', 'el_CY', 'cs_CZ', 'sk_SK', 'hu_HU', 'ro_RO', 'ro_MD', 'bg_BG', 'hr_HR', 'sl_SI', 'sr_RS', 'sr_Latn_RS', 'sr_Cyrl_RS', 'me_ME', 'lt_LT', 'lv_LV', 'et_EE', 'uk_UA'],
  '南美洲': ['es_PE', 'es_VE'],
  '非洲': ['en_NG', 'en_UG']
};

console.log('\n按地区分组的缺失locale：');
Object.entries(missingByRegion).forEach(([region, locales]) => {
  console.log(`${region}: ${locales.join(', ')}`);
});
