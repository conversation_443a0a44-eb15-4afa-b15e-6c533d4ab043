// 发票类型枚举
export enum InvoiceType {
  WINDSURF = 'windsurf',
  WINDSURF_15 = 'windsurf_15',
  CURSOR = 'cursor'
}

// 公司信息接口
export interface CompanyInfo {
  name: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  email?: string;
  phone?: string;
  taxInfo?: string;
}

// 基础发票数据接口
export interface InvoiceData {
  type: InvoiceType;
  invoiceNumber: string;
  receiptNumber: string;
  datePaid: string;
  paymentMethod: string;
  billTo: BillToInfo;
  amount: string;
  description: string;
  dateRange: string;
  companyInfo: CompanyInfo;
}

export interface BillToInfo {
  name: string;
  company: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  district?: string; // 区县信息
  street?: string;   // 街道信息
}

// 生成标准7行地址格式的函数
export function formatBillToAddress(billTo: BillToInfo): string {
  // 用户要求的格式：Bill to/姓名/邮编/省市信息/街道信息/区县信息/国家/邮箱
  const lines = [
    'Bill to',
    billTo.name,
    billTo.postalCode,
    `${billTo.state}${billTo.city}`, // 省市信息合并
    billTo.street || billTo.address, // 街道信息
    billTo.district || '', // 区县信息
    billTo.country,
    billTo.email
  ];

  return lines.filter(line => line.trim() !== '').join('\n');
}
