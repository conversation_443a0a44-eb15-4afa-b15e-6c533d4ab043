import fs from 'fs';
import path from 'path';
import { FLAG_MAPPING } from './flagMapping';

// 读取 localeConfig.ts 文件
const configPath = path.join(__dirname, 'localeConfig.ts');
let content = fs.readFileSync(configPath, 'utf-8');

// 为每个locale添加flag字段
Object.entries(FLAG_MAPPING).forEach(([localeCode, flag]) => {
  // 查找对应的locale配置
  const localePattern = new RegExp(`('${localeCode}':\\s*{[^}]*nameEn:\\s*'[^']*',)`, 'g');
  content = content.replace(localePattern, (match) => {
    // 在nameEn后面添加flag字段
    return match.replace(/(nameEn:\s*'[^']*',)/, `$1\n    flag: '${flag}',`);
  });
});

// 写回文件
fs.writeFileSync(configPath, content, 'utf-8');
console.log('国旗字段添加完成！');
