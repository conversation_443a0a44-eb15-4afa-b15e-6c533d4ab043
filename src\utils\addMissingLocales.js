const fs = require('fs');
const path = require('path');

// 读取缺失的locale配置
const missingConfigsPath = path.join(__dirname, 'missingLocaleConfigs.ts');
const missingConfigsContent = fs.readFileSync(missingConfigsPath, 'utf-8');

// 提取配置对象
const configMatch = missingConfigsContent.match(/export const MISSING_LOCALE_CONFIGS = ({[\s\S]*});/);
if (!configMatch) {
  console.error('无法解析缺失的locale配置');
  process.exit(1);
}

// 读取主配置文件
const configPath = path.join(__dirname, 'localeConfig.ts');
let content = fs.readFileSync(configPath, 'utf-8');

// 找到LOCALE_CONFIG的结束位置
const configEndMatch = content.match(/^};$/m);
if (!configEndMatch) {
  console.error('无法找到LOCALE_CONFIG的结束位置');
  process.exit(1);
}

// 准备要添加的配置
const configsToAdd = `
  // === 新增的locale配置 ===
  
  // 瑞士多语言
  'de_CH': {
    code: 'de_CH',
    nameZh: '瑞士德语',
    nameEn: 'Switzerland German',
    flag: '🇨🇭',
    region: 'Europe',
    country: 'Switzerland',
    states: ['Zürich', 'Bern', 'Basel', 'Geneva', 'Lausanne', 'Winterthur', 'Lucerne', 'St. Gallen', 'Lugano', 'Biel'],
    firstNames: ['Hans', 'Peter', 'Anna', 'Maria', 'Fritz', 'Elisabeth', 'Karl', 'Margrit', 'Walter', 'Ruth', 'Ernst', 'Verena', 'Paul', 'Ursula', 'Max', 'Heidi'],
    lastNames: ['Müller', 'Meier', 'Schmid', 'Keller', 'Weber', 'Huber', 'Schneider', 'Meyer', 'Steiner', 'Fischer', 'Gerber', 'Brunner', 'Baumann', 'Frei', 'Zimmermann', 'Moser']
  },
  'fr_CH': {
    code: 'fr_CH',
    nameZh: '瑞士法语',
    nameEn: 'Switzerland French',
    flag: '🇨🇭',
    region: 'Europe',
    country: 'Switzerland',
    states: ['Geneva', 'Lausanne', 'Neuchâtel', 'Fribourg', 'Sion', 'Montreux', 'Yverdon', 'La Chaux-de-Fonds', 'Delémont', 'Martigny'],
    firstNames: ['Jean', 'Pierre', 'Marie', 'Anne', 'Michel', 'Catherine', 'Philippe', 'Françoise', 'Alain', 'Sylvie', 'Bernard', 'Martine', 'Daniel', 'Nicole', 'Claude', 'Isabelle'],
    lastNames: ['Martin', 'Bernard', 'Dubois', 'Thomas', 'Robert', 'Richard', 'Petit', 'Durand', 'Leroy', 'Moreau', 'Simon', 'Laurent', 'Lefebvre', 'Michel', 'Garcia', 'David']
  },
  'it_CH': {
    code: 'it_CH',
    nameZh: '瑞士意大利语',
    nameEn: 'Switzerland Italian',
    flag: '🇨🇭',
    region: 'Europe',
    country: 'Switzerland',
    states: ['Lugano', 'Bellinzona', 'Locarno', 'Mendrisio', 'Chiasso', 'Ascona', 'Biasca', 'Airolo', 'Faido', 'Acquarossa'],
    firstNames: ['Marco', 'Giuseppe', 'Anna', 'Maria', 'Antonio', 'Francesca', 'Luigi', 'Paola', 'Francesco', 'Laura', 'Giovanni', 'Silvia', 'Paolo', 'Elena', 'Andrea', 'Giulia'],
    lastNames: ['Rossi', 'Ferrari', 'Russo', 'Bianchi', 'Romano', 'Gallo', 'Conti', 'Ricci', 'Marino', 'Greco', 'Bruno', 'Galli', 'Lombardi', 'Giordano', 'Mancini', 'Rizzo']
  },
  
  // 比利时多语言
  'fr_BE': {
    code: 'fr_BE',
    nameZh: '比利时法语',
    nameEn: 'Belgium French',
    flag: '🇧🇪',
    region: 'Europe',
    country: 'Belgium',
    states: ['Brussels', 'Wallonia', 'Liège', 'Charleroi', 'Namur', 'Mons', 'Tournai', 'Verviers', 'Seraing', 'La Louvière'],
    firstNames: ['Jean', 'Pierre', 'Marie', 'Anne', 'Michel', 'Catherine', 'Philippe', 'Françoise', 'Alain', 'Sylvie', 'Bernard', 'Martine', 'Daniel', 'Nicole', 'Claude', 'Isabelle'],
    lastNames: ['Peeters', 'Janssens', 'Maes', 'Jacobs', 'Mertens', 'Willems', 'Claes', 'Goossens', 'Wouters', 'De Smet', 'De Vries', 'Van Den Berg', 'Bakker', 'Visser', 'Smit', 'Meijer']
  },
  'nl_BE': {
    code: 'nl_BE',
    nameZh: '比利时荷兰语',
    nameEn: 'Belgium Dutch',
    flag: '🇧🇪',
    region: 'Europe',
    country: 'Belgium',
    states: ['Antwerp', 'Ghent', 'Bruges', 'Leuven', 'Mechelen', 'Aalst', 'Kortrijk', 'Hasselt', 'Sint-Niklaas', 'Ostend'],
    firstNames: ['Jan', 'Piet', 'Kees', 'Henk', 'Marie', 'Anna', 'Els', 'Inge', 'Tom', 'Luc', 'Marc', 'Peter', 'Johan', 'Filip', 'Dirk', 'Erik'],
    lastNames: ['Peeters', 'Janssens', 'Maes', 'Jacobs', 'Mertens', 'Willems', 'Claes', 'Goossens', 'Wouters', 'De Smet', 'De Vries', 'Van Den Berg', 'Bakker', 'Visser', 'Smit', 'Meijer']
  },
  
  // 葡萄牙
  'pt_PT': {
    code: 'pt_PT',
    nameZh: '葡萄牙',
    nameEn: 'Portugal',
    flag: '🇵🇹',
    region: 'Europe',
    country: 'Portugal',
    states: ['Lisbon', 'Porto', 'Braga', 'Coimbra', 'Funchal', 'Aveiro', 'Faro', 'Setúbal', 'Évora', 'Viseu'],
    firstNames: ['João', 'António', 'José', 'Francisco', 'Manuel', 'Pedro', 'Paulo', 'Carlos', 'Luís', 'Miguel', 'Maria', 'Ana', 'Joana', 'Catarina', 'Inês', 'Sofia'],
    lastNames: ['Silva', 'Santos', 'Ferreira', 'Pereira', 'Oliveira', 'Costa', 'Rodrigues', 'Martins', 'Jesus', 'Sousa', 'Fernandes', 'Gonçalves', 'Gomes', 'Lopes', 'Marques', 'Alves']
  },

  // 南美洲
  'es_PE': {
    code: 'es_PE',
    nameZh: '秘鲁',
    nameEn: 'Peru',
    flag: '🇵🇪',
    region: 'South America',
    country: 'Peru',
    states: ['Lima', 'Arequipa', 'Trujillo', 'Chiclayo', 'Piura', 'Iquitos', 'Cusco', 'Chimbote', 'Huancayo', 'Tacna'],
    firstNames: ['José', 'Juan', 'Luis', 'Carlos', 'Miguel', 'Antonio', 'Francisco', 'Manuel', 'Pedro', 'Jesús', 'María', 'Ana', 'Carmen', 'Rosa', 'Juana', 'Isabel'],
    lastNames: ['García', 'Rodríguez', 'González', 'Fernández', 'López', 'Martínez', 'Sánchez', 'Pérez', 'Gómez', 'Martín', 'Jiménez', 'Ruiz', 'Hernández', 'Díaz', 'Moreno', 'Muñoz']
  },
  'es_VE': {
    code: 'es_VE',
    nameZh: '委内瑞拉',
    nameEn: 'Venezuela',
    flag: '🇻🇪',
    region: 'South America',
    country: 'Venezuela',
    states: ['Caracas', 'Maracaibo', 'Valencia', 'Barquisimeto', 'Maracay', 'Ciudad Guayana', 'San Cristóbal', 'Maturín', 'Ciudad Bolívar', 'Cumana'],
    firstNames: ['José', 'Juan', 'Luis', 'Carlos', 'Miguel', 'Antonio', 'Francisco', 'Manuel', 'Pedro', 'Jesús', 'María', 'Ana', 'Carmen', 'Rosa', 'Juana', 'Isabel'],
    lastNames: ['García', 'Rodríguez', 'González', 'Fernández', 'López', 'Martínez', 'Sánchez', 'Pérez', 'Gómez', 'Martín', 'Jiménez', 'Ruiz', 'Hernández', 'Díaz', 'Moreno', 'Muñoz']
  },

  // 非洲
  'en_NG': {
    code: 'en_NG',
    nameZh: '尼日利亚',
    nameEn: 'Nigeria',
    flag: '🇳🇬',
    region: 'Africa',
    country: 'Nigeria',
    states: ['Lagos', 'Kano', 'Ibadan', 'Kaduna', 'Port Harcourt', 'Benin City', 'Maiduguri', 'Zaria', 'Aba', 'Jos'],
    firstNames: ['Adebayo', 'Chinedu', 'Emeka', 'Funmi', 'Kemi', 'Olumide', 'Segun', 'Tunde', 'Yemi', 'Zainab', 'Amina', 'Blessing', 'Chioma', 'Fatima', 'Grace', 'Hauwa'],
    lastNames: ['Adebayo', 'Okafor', 'Okonkwo', 'Eze', 'Nwankwo', 'Okoro', 'Onyeka', 'Chukwu', 'Obi', 'Emeka', 'Ikechukwu', 'Chima', 'Kalu', 'Nnaji', 'Ugwu', 'Anyanwu']
  },
  'en_UG': {
    code: 'en_UG',
    nameZh: '乌干达',
    nameEn: 'Uganda',
    flag: '🇺🇬',
    region: 'Africa',
    country: 'Uganda',
    states: ['Kampala', 'Gulu', 'Lira', 'Mbarara', 'Jinja', 'Bwizibwera', 'Mbale', 'Mukono', 'Kasese', 'Masaka'],
    firstNames: ['John', 'Mary', 'Paul', 'Sarah', 'David', 'Grace', 'Peter', 'Jane', 'Moses', 'Ruth', 'Samuel', 'Rebecca', 'Joseph', 'Esther', 'Daniel', 'Joyce'],
    lastNames: ['Musoke', 'Nakamura', 'Ssebunya', 'Kato', 'Namukasa', 'Mukasa', 'Ssali', 'Namatovu', 'Ssekandi', 'Nambi', 'Ssemakula', 'Nakalembe', 'Ssentongo', 'Nakato', 'Sserwanga', 'Namugga']
  },`;

// 在最后的 }; 之前插入新配置
content = content.replace(/^};$/m, configsToAdd + '\n};');

// 写回文件
fs.writeFileSync(configPath, content, 'utf-8');
console.log('成功添加缺失的locale配置！');
console.log('新增了以下locale：');
console.log('- 瑞士多语言: de_CH, fr_CH, it_CH');
console.log('- 比利时多语言: fr_BE, nl_BE');
console.log('- 葡萄牙: pt_PT');
console.log('- 南美洲: es_PE, es_VE');
console.log('- 非洲: en_NG, en_UG');
