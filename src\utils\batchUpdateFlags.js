const fs = require('fs');
const path = require('path');

// 国旗映射表
const flagMapping = {
  'en_HK': '🇭🇰', 'en_SG': '🇸🇬', 'ms_MY': '🇲🇾', 'th_TH': '🇹🇭', 'vi_VN': '🇻🇳',
  'en_PH': '🇵🇭', 'id_ID': '🇮🇩', 'en_IN': '🇮🇳', 'bn_BD': '🇧🇩', 'ne_NP': '🇳🇵',
  'fa_IR': '🇮🇷', 'he_IL': '🇮🇱', 'tr_TR': '🇹🇷', 'kk_KZ': '🇰🇿', 'mn_MN': '🇲🇳',
  'hy_AM': '🇦🇲', 'ka_GE': '🇬🇪', 'en_GB': '🇬🇧', 'de_DE': '🇩🇪', 'fr_FR': '🇫🇷',
  'it_IT': '🇮🇹', 'es_ES': '🇪🇸', 'ru_RU': '🇷🇺', 'pl_PL': '🇵🇱', 'nl_NL': '🇳🇱',
  'sv_SE': '🇸🇪', 'nb_NO': '🇳🇴', 'da_DK': '🇩🇰', 'fi_FI': '🇫🇮', 'is_IS': '🇮🇸',
  'at_AT': '🇦🇹', 'de_AT': '🇦🇹', 'en_CA': '🇨🇦', 'fr_CA': '🇨🇦', 'pt_BR': '🇧🇷',
  'es_AR': '🇦🇷', 'ar_EG': '🇪🇬', 'en_ZA': '🇿🇦', 'en_AU': '🇦🇺', 'en_NZ': '🇳🇿',
  'ar_JO': '🇯🇴', 'ar_SA': '🇸🇦'
};

// 读取文件
const configPath = path.join(__dirname, 'localeConfig.ts');
let content = fs.readFileSync(configPath, 'utf-8');

// 批量添加flag字段
Object.entries(flagMapping).forEach(([localeCode, flag]) => {
  // 查找对应的locale配置并添加flag字段
  const pattern = new RegExp(`(\\s+'${localeCode}':\\s*{[^}]*nameEn:\\s*'[^']*',)`, 'g');
  content = content.replace(pattern, (match) => {
    if (match.includes('flag:')) {
      return match; // 已经有flag字段了，跳过
    }
    return match.replace(/(nameEn:\s*'[^']*',)/, `$1\n    flag: '${flag}',`);
  });
});

// 写回文件
fs.writeFileSync(configPath, content, 'utf-8');
console.log('批量添加国旗完成！');
