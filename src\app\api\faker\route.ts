import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const locale = searchParams.get('locale') || 'en_US';
  const quantity = searchParams.get('quantity') || '1';

  try {
    console.log(`🌐 服务器端调用 Faker API (${locale})...`);
    
    const response = await fetch(`https://fakerapi.it/api/v2/persons?_quantity=${quantity}&_locale=${locale}`, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'WIPDF-Invoice-Generator'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log(`✅ 服务器端 Faker API 响应成功`, data);

    return NextResponse.json(data);
  } catch (error) {
    console.error('服务器端 Faker API 调用失败:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '未知错误' },
      { status: 500 }
    );
  }
}
