// 国际地址格式数据 - 基于 Wikipedia 和 UPU 标准
export interface AddressFormat {
  code: string;
  format: string;
  buildingFormat: string;
}

// 邮政编码格式接口
export interface PostalCodeFormat {
  code: string; // ISO 3166-1 alpha-2 国家代码
  name: string; // 国家名称
  postalCodeFormat: string; // 邮政编码格式 (N=数字, A=字母, ?=数字或字母, CC=国家代码)
  postalCodeRegex: string; // 正则表达式验证
  hasPostalCode: boolean; // 是否使用邮政编码
  example?: string; // 示例邮政编码
  note?: string; // 特殊说明
}

// 地址格式映射表 (前100个国家)
export const ADDRESS_FORMATS: AddressFormat[] = [
  { code: "AD", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [postcode] [settlement], [country]" },
  { code: "AE", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement_part]\\n[settlement]\\n[region]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement_part], [settlement], [region], [country]" },
  { code: "AF", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement_part]\\n[settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement_part], [settlement], [country]" },
  { code: "AG", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "AI", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement]\\n[postcode] [country]", buildingFormat: "[house], [road] [house_number], [settlement], [postcode] [country]" },
  { code: "AL", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "AM", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[postcode]\\n[settlement]\\n[region]\\n[country]", buildingFormat: "[house], [house_number] [road], [postcode], [settlement], [region], [country]" },
  { code: "AO", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement], [postcode]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement], [postcode], [country]" },
  { code: "AQ", format: "[contact_name]\\n[house]\\n[settlement]\\n[country]", buildingFormat: "[house], [settlement], [country]" },
  { code: "AR", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement_part]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement_part], [postcode] [settlement], [country]" },
  { code: "AS", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement], [region] [postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [region] [postcode], [country]" },
  { code: "AT", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "AU", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement] [region] [postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement] [region] [postcode], [country]" },
  { code: "AW", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement], [country]" },
  { code: "AX", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "AZ", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [postcode] [settlement], [country]" },
  { code: "BA", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "BB", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "BD", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement_part]\\n[settlement]- [postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement_part], [settlement]- [postcode], [country]" },
  { code: "BE", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "BF", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[county]\\n[region]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [county], [region], [country]" },
  { code: "BG", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement_part]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement_part], [postcode] [settlement], [country]" },
  { code: "BH", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement] [postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement] [postcode], [country]" },
  { code: "BI", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement], [country]" },
  { code: "BJ", format: "[contact_name]\\n[house]\\n[house_number], [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number], [road], [settlement], [country]" },
  { code: "BL", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [postcode] [settlement], [country]" },
  { code: "BM", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement] [postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement] [postcode], [country]" },
  { code: "BN", format: "[contact_name]\\n[house]\\n[house_number], [road]\\n[settlement]\\n[region] [postcode]\\n[country]", buildingFormat: "[house], [house_number], [road], [settlement], [region] [postcode], [country]" },
  { code: "BO", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement], [country]" },
  { code: "BQ", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "BR", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement_part]\\n[settlement] - [region]\\n[postcode]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement_part], [settlement] - [region], [postcode], [country]" },
  { code: "BS", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement]\\n[county]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement], [county], [country]" },
  { code: "BT", format: "[contact_name]\\n[house]\\n[road] [house_number], [house]\\n[settlement_part]\\n[settlement] [postcode]\\n[country]", buildingFormat: "[house], [road] [house_number], [house], [settlement_part], [settlement] [postcode], [country]" },
  { code: "BV", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "BW", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement_part]\\n[settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement_part], [settlement], [country]" },
  { code: "BY", format: "[country]\\n[region]\\n[postcode] [settlement]\\n[suburb]\\n[road], [house_number]\\n[house]\\n[contact_name]", buildingFormat: "[country], [region], [postcode] [settlement], [suburb], [road], [house_number], [house]" },
  { code: "BZ", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "CA", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement], [region] [postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [region] [postcode], [country]" },
  { code: "CC", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement] [region] [postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement] [region] [postcode], [country]" },
  { code: "CD", format: "[contact_name]\\n[house]\\n[house_number], [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number], [road], [settlement], [country]" },
  { code: "CF", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement], [country]" },
  { code: "CG", format: "[contact_name]\\n[house]\\n[house_number], [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number], [road], [settlement], [country]" },
  { code: "CH", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "CI", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "CK", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "CL", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "CM", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement], [country]" },
  { code: "CN", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement_part]\\n[county]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement_part], [county], [postcode] [settlement], [country]" },
  { code: "CN_en", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement_part]\\n[county]\\n[postcode][settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement_part], [county], [postcode][settlement], [country]" },
  { code: "CN_zh", format: "[country]\\n[postcode]\\n[region]\\n[county]\\n[settlement]\\n[settlement_part]\\n[road]\\n[house_number]\\n[house]\\n[contact_name]", buildingFormat: "[country], [postcode], [region], [county], [settlement], [settlement_part], [road], [house_number], [house]" },
  { code: "CO", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement_part]\\n[postcode] [settlement], [region]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement_part], [postcode] [settlement], [region], [country]" },
  { code: "CR", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement_part]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement_part], [postcode] [settlement], [country]" },
  { code: "CU", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement]\\n[postcode] [region]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement], [postcode] [region], [country]" },
  { code: "CV", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [postcode] [settlement], [country]" },
  { code: "CW", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement], [country]" },
  { code: "CX", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement] [region] [postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement] [region] [postcode], [country]" },
  { code: "CY", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [postcode] [settlement], [country]" },
  { code: "CZ", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "DE", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "DJ", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "DK", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "DM", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "DO", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement_part]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement_part], [postcode] [settlement], [country]" },
  { code: "DZ", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [postcode] [settlement], [country]" },
  { code: "EC", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement]\\n[postcode]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement], [postcode], [country]" },
  { code: "EE", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "EG", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement_part]\\n[settlement]\\n[region] [postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement_part], [settlement], [region] [postcode], [country]" },
  { code: "EH", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [postcode] [settlement], [country]" },
  { code: "ER", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "ES", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "ET", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [postcode], [country]" },
  { code: "FI", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "FJ", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "FK", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [postcode], [country]" },
  { code: "FM", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement], [region] [postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [region] [postcode], [country]" },
  { code: "FO", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "FR", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [postcode] [settlement], [country]" },
  { code: "GA", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "GB", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[county]\\n[postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [county], [postcode], [country]" },
  { code: "GD", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "GE", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [postcode] [settlement], [country]" },
  { code: "GF", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [postcode] [settlement], [country]" },
  { code: "GG", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [postcode], [country]" },
  { code: "GH", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "GI", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [postcode], [country]" },
  { code: "GL", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "GM", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "GN", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "GP", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [postcode] [settlement], [country]" },
  { code: "GQ", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "GR", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "GS", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [postcode], [country]" },
  { code: "GT", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement]\\n[postcode]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement], [postcode], [country]" },
  { code: "GU", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement], [region] [postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [region] [postcode], [country]" },
  { code: "GW", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [postcode] [settlement], [country]" },
  { code: "GY", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [country]" },
  { code: "HK", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement_part]\\n[settlement]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement_part], [settlement], [country]" },
  { code: "HM", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement] [region] [postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement] [region] [postcode], [country]" },
  { code: "HN", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[settlement]\\n[postcode]\\n[country]", buildingFormat: "[house], [road] [house_number], [settlement], [postcode], [country]" },
  { code: "HR", format: "[contact_name]\\n[house]\\n[road] [house_number]\\n[postcode] [settlement]\\n[country]", buildingFormat: "[house], [road] [house_number], [postcode] [settlement], [country]" },
  { code: "HT", format: "[contact_name]\\n[house]\\n[house_number] [road]\\n[settlement]\\n[postcode]\\n[country]", buildingFormat: "[house], [house_number] [road], [settlement], [postcode], [country]" },
  { code: "HU", format: "[contact_name]\\n[house]\\n[settlement]\\n[road] [house_number]\\n[postcode]\\n[country]", buildingFormat: "[house], [settlement], [road] [house_number], [postcode], [country]" }
];

// 全球邮政编码格式数据 - 基于 Wikipedia List of postal codes
export const POSTAL_CODE_FORMATS: PostalCodeFormat[] = [
  { code: "AD", name: "Andorra", postalCodeFormat: "CCNNN", postalCodeRegex: "^AD\\d{3}$", hasPostalCode: true, example: "AD100", note: "Each parish has its own post code" },
  { code: "AE", name: "United Arab Emirates", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false, note: "Uses post office system instead" },
  { code: "AF", name: "Afghanistan", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "1001", note: "First two digits: province, last two: city/district" },
  { code: "AG", name: "Antigua and Barbuda", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "AI", name: "Anguilla", postalCodeFormat: "AI-2640", postalCodeRegex: "^AI-2640$", hasPostalCode: true, example: "AI-2640", note: "Single code for all addresses" },
  { code: "AL", name: "Albania", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "1001" },
  { code: "AM", name: "Armenia", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "0010" },
  { code: "AO", name: "Angola", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "AQ", name: "Antarctica", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "AR", name: "Argentina", postalCodeFormat: "ANNNNAAA", postalCodeRegex: "^[A-Z]\\d{4}[A-Z]{3}$", hasPostalCode: true, example: "C1000AAA", note: "CPA format: province code + 4 digits + 3 letters" },
  { code: "AS", name: "American Samoa", postalCodeFormat: "NNNNN", postalCodeRegex: "^96799$", hasPostalCode: true, example: "96799", note: "US ZIP code 96799" },
  { code: "AT", name: "Austria", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "1010" },
  { code: "AU", name: "Australia", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "2000", note: "First digit identifies state/territory" },
  { code: "AW", name: "Aruba", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "AX", name: "Åland Islands", postalCodeFormat: "NNNNN", postalCodeRegex: "^22\\d{3}$", hasPostalCode: true, example: "22100", note: "Finnish postal codes starting with 22" },
  { code: "AZ", name: "Azerbaijan", postalCodeFormat: "CC NNNN", postalCodeRegex: "^AZ \\d{4}$", hasPostalCode: true, example: "AZ 1000" },
  { code: "BA", name: "Bosnia and Herzegovina", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "71000" },
  { code: "BB", name: "Barbados", postalCodeFormat: "CCNNNNN", postalCodeRegex: "^BB\\d{5}$", hasPostalCode: true, example: "BB11000" },
  { code: "BD", name: "Bangladesh", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "1000" },
  { code: "BE", name: "Belgium", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "1000", note: "First digit indicates province" },
  { code: "BF", name: "Burkina Faso", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "BG", name: "Bulgaria", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "1000" },
  { code: "BH", name: "Bahrain", postalCodeFormat: "NNN", postalCodeRegex: "^\\d{3,4}$", hasPostalCode: true, example: "317", note: "3 or 4 digits, known as block number" },
  { code: "BI", name: "Burundi", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "BJ", name: "Benin", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "BL", name: "Saint Barthélemy", postalCodeFormat: "97133", postalCodeRegex: "^97133$", hasPostalCode: true, example: "97133", note: "French overseas collectivity" },
  { code: "BM", name: "Bermuda", postalCodeFormat: "AA NN", postalCodeRegex: "^[A-Z]{2} [A-Z0-9]{2}$", hasPostalCode: true, example: "HM 12", note: "AA NN for streets, AA AA for P.O. boxes" },
  { code: "BN", name: "Brunei", postalCodeFormat: "AANNNN", postalCodeRegex: "^[A-Z]{2}\\d{4}$", hasPostalCode: true, example: "BT1234" },
  { code: "BO", name: "Bolivia", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "BQ", name: "Bonaire, Sint Eustatius and Saba", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "BR", name: "Brazil", postalCodeFormat: "NNNNN-NNN", postalCodeRegex: "^\\d{5}-\\d{3}$", hasPostalCode: true, example: "01310-100", note: "CEP format" },
  { code: "BS", name: "Bahamas", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false, note: "Uses Post Office system" },
  { code: "BT", name: "Bhutan", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "11001" },
  { code: "BV", name: "Bouvet Island", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "BW", name: "Botswana", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "BY", name: "Belarus", postalCodeFormat: "NNNNNN", postalCodeRegex: "^\\d{6}$", hasPostalCode: true, example: "220050" },
  { code: "BZ", name: "Belize", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "CA", name: "Canada", postalCodeFormat: "ANA NAN", postalCodeRegex: "^[A-Z]\\d[A-Z] \\d[A-Z]\\d$", hasPostalCode: true, example: "K1A 0A6", note: "Letters D,F,I,O,Q,U not used" },
  { code: "CC", name: "Cocos (Keeling) Islands", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "6799", note: "Australian postal system" },
  { code: "CD", name: "Democratic Republic of the Congo", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "CF", name: "Central African Republic", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "CG", name: "Republic of the Congo", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "CH", name: "Switzerland", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "8001", note: "Ordered west to east" },
  { code: "CI", name: "Côte d'Ivoire", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "CK", name: "Cook Islands", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "CL", name: "Chile", postalCodeFormat: "NNNNNNN", postalCodeRegex: "^\\d{7}$", hasPostalCode: true, example: "8320000", note: "May only be required for bulk mail" },
  { code: "CM", name: "Cameroon", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "CN", name: "China", postalCodeFormat: "NNNNNN", postalCodeRegex: "^\\d{6}$", hasPostalCode: true, example: "100000", note: "First two digits indicate province" },
  { code: "CO", name: "Colombia", postalCodeFormat: "NNNNNN", postalCodeRegex: "^\\d{6}$", hasPostalCode: true, example: "110111", note: "First two digits indicate department" },
  { code: "CR", name: "Costa Rica", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "10101" },
  { code: "CU", name: "Cuba", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "10100", note: "CP prefix often used" },
  { code: "CV", name: "Cape Verde", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "7600", note: "First digit indicates island" },
  { code: "CW", name: "Curaçao", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "CX", name: "Christmas Island", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "6798", note: "Australian postal system" },
  { code: "CY", name: "Cyprus", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "1010" },
  { code: "CZ", name: "Czech Republic", postalCodeFormat: "NNN NN", postalCodeRegex: "^\\d{3} \\d{2}$", hasPostalCode: true, example: "110 00", note: "First digit 1-7" },
  { code: "DE", name: "Germany", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "10115" },
  { code: "DJ", name: "Djibouti", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "DK", name: "Denmark", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "1050", note: "Also used by Greenland" },
  { code: "DM", name: "Dominica", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "DO", name: "Dominican Republic", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "10101" },
  { code: "DZ", name: "Algeria", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "16000", note: "First two digits as in ISO 3166-2:DZ" },
  { code: "EC", name: "Ecuador", postalCodeFormat: "NNNNNN", postalCodeRegex: "^\\d{6}$", hasPostalCode: true, example: "170150" },
  { code: "EE", name: "Estonia", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "10101" },
  { code: "EG", name: "Egypt", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "11511" },
  { code: "EH", name: "Western Sahara", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "70000", note: "Moroccan postal system" },
  { code: "ER", name: "Eritrea", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "ES", name: "Spain", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "28001", note: "First two digits indicate province" },
  { code: "ET", name: "Ethiopia", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "1000", note: "Trial basis for Addis Ababa" },
  { code: "FI", name: "Finland", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "00100", note: "Lower first digit = south, higher = north" },
  { code: "FJ", name: "Fiji", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "FK", name: "Falkland Islands", postalCodeFormat: "FIQQ 1ZZ", postalCodeRegex: "^FIQQ 1ZZ$", hasPostalCode: true, example: "FIQQ 1ZZ", note: "Single code" },
  { code: "FM", name: "Micronesia", postalCodeFormat: "NNNNN", postalCodeRegex: "^969(39|40)$", hasPostalCode: true, example: "96939", note: "US ZIP codes" },
  { code: "FO", name: "Faroe Islands", postalCodeFormat: "CC-NNN", postalCodeRegex: "^FO-\\d{3}$", hasPostalCode: true, example: "FO-100" },
  { code: "FR", name: "France", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "75001", note: "First two digits indicate département" },
  { code: "GA", name: "Gabon", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "GB", name: "United Kingdom", postalCodeFormat: "A[A]N[A/N] NAA", postalCodeRegex: "^[A-Z]{1,2}\\d[A-Z\\d]? \\d[A-Z]{2}$", hasPostalCode: true, example: "SW1A 1AA", note: "Complex format with outward and inward codes" },
  { code: "GD", name: "Grenada", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "GE", name: "Georgia", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "0108" },
  { code: "GF", name: "French Guiana", postalCodeFormat: "973NN", postalCodeRegex: "^973\\d{2}$", hasPostalCode: true, example: "97300", note: "French overseas department" },
  { code: "GG", name: "Guernsey", postalCodeFormat: "GY? ?NAA", postalCodeRegex: "^GY\\d{1,2} \\d[A-Z]{2}$", hasPostalCode: true, example: "GY1 1AA", note: "UK format with GY prefix" },
  { code: "GH", name: "Ghana", postalCodeFormat: "CCNNNNNNN", postalCodeRegex: "^[A-Z]{2}\\d{7}$", hasPostalCode: true, example: "GA1234567", note: "GhanaPostGPS system" },
  { code: "GI", name: "Gibraltar", postalCodeFormat: "GX11 1AA", postalCodeRegex: "^GX11 1AA$", hasPostalCode: true, example: "GX11 1AA", note: "Single code" },
  { code: "GL", name: "Greenland", postalCodeFormat: "NNNN", postalCodeRegex: "^39\\d{2}$", hasPostalCode: true, example: "3900", note: "Danish postal system with 39xx range" },
  { code: "GM", name: "Gambia", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "GN", name: "Guinea", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "GP", name: "Guadeloupe", postalCodeFormat: "971NN", postalCodeRegex: "^971\\d{2}$", hasPostalCode: true, example: "97100", note: "French overseas department" },
  { code: "GQ", name: "Equatorial Guinea", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "GR", name: "Greece", postalCodeFormat: "NNN NN", postalCodeRegex: "^\\d{3} \\d{2}$", hasPostalCode: true, example: "104 32" },
  { code: "GS", name: "South Georgia and South Sandwich Islands", postalCodeFormat: "SIQQ 1ZZ", postalCodeRegex: "^SIQQ 1ZZ$", hasPostalCode: true, example: "SIQQ 1ZZ", note: "Single code" },
  { code: "GT", name: "Guatemala", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "01001" },
  { code: "GU", name: "Guam", postalCodeFormat: "NNNNN", postalCodeRegex: "^969\\d{2}$", hasPostalCode: true, example: "96910", note: "US ZIP codes" },
  { code: "GW", name: "Guinea-Bissau", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "1000" },
  { code: "GY", name: "Guyana", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false },
  { code: "HK", name: "Hong Kong", postalCodeFormat: "", postalCodeRegex: "", hasPostalCode: false, note: "No postal code system" },
  { code: "HM", name: "Heard Island and McDonald Islands", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "7151", note: "Australian postal system" },
  { code: "HN", name: "Honduras", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "11101" },
  { code: "HR", name: "Croatia", postalCodeFormat: "NNNNN", postalCodeRegex: "^\\d{5}$", hasPostalCode: true, example: "10000" },
  { code: "HT", name: "Haiti", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "6110" },
  { code: "HU", name: "Hungary", postalCodeFormat: "NNNN", postalCodeRegex: "^\\d{4}$", hasPostalCode: true, example: "1011" }
];

// 国家代码到locale的映射
export const COUNTRY_TO_LOCALE_MAP: { [key: string]: string } = {
  'AD': 'es_ES', // Andorra
  'AE': 'ar_SA', // United Arab Emirates
  'AF': 'fa_IR', // Afghanistan
  'AG': 'en_US', // Antigua and Barbuda
  'AI': 'en_GB', // Anguilla
  'AL': 'sq_AL', // Albania
  'AM': 'hy_AM', // Armenia
  'AO': 'pt_PT', // Angola
  'AQ': 'en_US', // Antarctica
  'AR': 'es_AR', // Argentina
  'AS': 'en_US', // American Samoa
  'AT': 'de_AT', // Austria
  'AU': 'en_AU', // Australia
  'AW': 'nl_NL', // Aruba
  'AX': 'sv_SE', // Åland Islands
  'AZ': 'az_AZ', // Azerbaijan
  'BA': 'bs_BA', // Bosnia and Herzegovina
  'BB': 'en_US', // Barbados
  'BD': 'bn_BD', // Bangladesh
  'BE': 'fr_BE', // Belgium
  'BF': 'fr_FR', // Burkina Faso
  'BG': 'bg_BG', // Bulgaria
  'BH': 'ar_SA', // Bahrain
  'BI': 'fr_FR', // Burundi
  'BJ': 'fr_FR', // Benin
  'BL': 'fr_FR', // Saint Barthélemy
  'BM': 'en_GB', // Bermuda
  'BN': 'ms_MY', // Brunei
  'BO': 'es_ES', // Bolivia
  'BQ': 'nl_NL', // Bonaire, Sint Eustatius and Saba
  'BR': 'pt_BR', // Brazil
  'BS': 'en_US', // Bahamas
  'BT': 'dz_BT', // Bhutan
  'BV': 'nb_NO', // Bouvet Island
  'BW': 'en_ZA', // Botswana
  'BY': 'be_BY', // Belarus
  'BZ': 'en_US', // Belize
  'CA': 'en_CA', // Canada
  'CC': 'en_AU', // Cocos (Keeling) Islands
  'CD': 'fr_FR', // Democratic Republic of the Congo
  'CF': 'fr_FR', // Central African Republic
  'CG': 'fr_FR', // Republic of the Congo
  'CH': 'de_CH', // Switzerland
  'CI': 'fr_FR', // Côte d'Ivoire
  'CK': 'en_NZ', // Cook Islands
  'CL': 'es_ES', // Chile
  'CM': 'fr_FR', // Cameroon
  'CN': 'zh_CN', // China
  'CO': 'es_ES', // Colombia
  'CR': 'es_ES', // Costa Rica
  'CU': 'es_ES', // Cuba
  'CV': 'pt_PT', // Cape Verde
  'CW': 'nl_NL', // Curaçao
  'CX': 'en_AU', // Christmas Island
  'CY': 'el_CY', // Cyprus
  'CZ': 'cs_CZ', // Czech Republic
  'DE': 'de_DE', // Germany
  'DJ': 'fr_FR', // Djibouti
  'DK': 'da_DK', // Denmark
  'DM': 'en_US', // Dominica
  'DO': 'es_ES', // Dominican Republic
  'DZ': 'ar_SA', // Algeria
  'EC': 'es_ES', // Ecuador
  'EE': 'et_EE', // Estonia
  'EG': 'ar_EG', // Egypt
  'EH': 'ar_SA', // Western Sahara
  'ER': 'ti_ER', // Eritrea
  'ES': 'es_ES', // Spain
  'ET': 'am_ET', // Ethiopia
  'FI': 'fi_FI', // Finland
  'FJ': 'en_US', // Fiji
  'FK': 'en_GB', // Falkland Islands
  'FM': 'en_US', // Micronesia
  'FO': 'fo_FO', // Faroe Islands
  'FR': 'fr_FR', // France
  'GA': 'fr_FR', // Gabon
  'GB': 'en_GB', // United Kingdom
  'GD': 'en_US', // Grenada
  'GE': 'ka_GE', // Georgia
  'GF': 'fr_FR', // French Guiana
  'GG': 'en_GB', // Guernsey
  'GH': 'en_US', // Ghana
  'GI': 'en_GB', // Gibraltar
  'GL': 'kl_GL', // Greenland
  'GM': 'en_US', // Gambia
  'GN': 'fr_FR', // Guinea
  'GP': 'fr_FR', // Guadeloupe
  'GQ': 'es_ES', // Equatorial Guinea
  'GR': 'el_GR', // Greece
  'GS': 'en_GB', // South Georgia and the South Sandwich Islands
  'GT': 'es_ES', // Guatemala
  'GU': 'en_US', // Guam
  'GW': 'pt_PT', // Guinea-Bissau
  'GY': 'en_US', // Guyana
  'HK': 'en_HK', // Hong Kong
  'HM': 'en_AU', // Heard Island and McDonald Islands
  'HN': 'es_ES', // Honduras
  'HR': 'hr_HR', // Croatia
  'HT': 'fr_FR', // Haiti
  'HU': 'hu_HU', // Hungary
};

// 获取国家对应的地址格式
export function getAddressFormatByCountryCode(countryCode: string): AddressFormat | null {
  return ADDRESS_FORMATS.find(format => format.code === countryCode) || null;
}

// 获取国家对应的locale
export function getLocaleByCountryCode(countryCode: string): string {
  return COUNTRY_TO_LOCALE_MAP[countryCode] || 'en_US';
}